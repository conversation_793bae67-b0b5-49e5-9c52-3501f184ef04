package com.example.biaozhu.repository;

import com.example.biaozhu.entity.Annotation;
import com.example.biaozhu.entity.Annotation.AnnotationStatus;
import com.example.biaozhu.entity.Annotation.AnnotationType;
import com.example.biaozhu.entity.Annotation.ReviewStatus;
import com.example.biaozhu.entity.DataItem;
import com.example.biaozhu.entity.Task;
import com.example.biaozhu.entity.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 标注仓库接口
 */
@Repository
public interface AnnotationRepository extends JpaRepository<Annotation, Long> {
    
    /**
     * 根据任务查找标注
     * @param task 任务
     * @return 标注列表
     */
    List<Annotation> findByTask(Task task);
    
    /**
     * 根据数据项查找标注
     * @param dataItem 数据项
     * @return 标注列表
     */
    List<Annotation> findByDataItem(DataItem dataItem);
    
    /**
     * 根据创建者查找标注
     * @param creator 创建者
     * @param pageable 分页参数
     * @return 标注分页结果
     */
    Page<Annotation> findByCreator(User creator, Pageable pageable);
    
    /**
     * 根据任务和数据项查找标注
     * @param task 任务
     * @param dataItem 数据项
     * @return 标注列表或可选项
     */
    Optional<Annotation> findByTaskAndDataItem(Task task, DataItem dataItem);
    
    /**
     * 根据任务和创建者查找标注
     * @param task 任务
     * @param creator 创建者
     * @return 标注列表
     */
    List<Annotation> findByTaskAndCreator(Task task, User creator);
    
    /**
     * 根据标注者查找标注（标注者实际上是creator）
     * @param annotator 标注者
     * @return 标注列表
     */
    @Query("SELECT a FROM Annotation a WHERE a.creator = :annotator")
    List<Annotation> findByAnnotator(@Param("annotator") User annotator);
    
    /**
     * 根据审核者查找标注
     * @param reviewer 审核者
     * @return 标注列表
     */
    List<Annotation> findByReviewer(User reviewer);
    
    /**
     * 根据标注状态查找标注
     * @param status 标注状态（使用枚举类型）
     * @return 标注列表
     */
    List<Annotation> findByStatus(AnnotationStatus status);
    
    /**
     * 根据审核状态查找标注
     * @param status 审核状态（使用字符串类型）
     * @return 标注列表
     */
    @Query("SELECT a FROM Annotation a WHERE a.status = :status")
    List<Annotation> findByStatusString(@Param("status") String status);
    
    /**
     * 根据标注类型查找标注
     * @param type 标注类型
     * @return 标注列表
     */
    List<Annotation> findByType(AnnotationType type);
    
    /**
     * 查找指定时间段内创建的标注
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 标注列表
     */
    List<Annotation> findByCreatedAtBetween(LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 根据置信度范围查找标注
     * @param minConfidence 最小置信度
     * @param maxConfidence 最大置信度
     * @return 标注列表
     */
    List<Annotation> findByConfidenceBetween(double minConfidence, double maxConfidence);
    
    /**
     * 查找需要审核的标注（分页）
     * @param status 审核状态
     * @param pageable 分页参数
     * @return 分页标注结果
     */
    @Query("SELECT a FROM Annotation a WHERE a.status = :status")
    Page<Annotation> findByStatusString(@Param("status") String status, Pageable pageable);
    
    /**
     * 统计任务的标注数量
     * @param task 任务
     * @return 标注数量
     */
    Long countByTask(Task task);

    /**
     * 根据任务ID查找未审核的标注（分页）
     * @param taskId 任务ID
     * @param pageable 分页参数
     * @return 分页标注结果
     */
    @Query("SELECT a FROM Annotation a LEFT JOIN FETCH a.creator LEFT JOIN FETCH a.dataItem LEFT JOIN FETCH a.task WHERE a.task.id = :taskId AND a.status = 'SUBMITTED' AND a.reviewedAt IS NULL")
    Page<Annotation> findUnreviewedByTaskId(@Param("taskId") Long taskId, Pageable pageable);

    /**
     * 根据任务ID查找未审核的标注（列表）
     * @param taskId 任务ID
     * @return 标注列表
     */
    @Query("SELECT a FROM Annotation a LEFT JOIN FETCH a.creator LEFT JOIN FETCH a.dataItem LEFT JOIN FETCH a.task WHERE a.task.id = :taskId AND a.status = 'SUBMITTED' AND a.reviewedAt IS NULL")
    List<Annotation> findUnreviewedByTaskId(@Param("taskId") Long taskId);
    
    /**
     * 统计数据项的标注数量
     * @param dataItem 数据项
     * @return 标注数量
     */
    Long countByDataItem(DataItem dataItem);
    
    /**
     * 统计用户的标注数量
     * @param creator 创建者
     * @return 标注数量
     */
    Long countByCreator(User creator);
    
    /**
     * 删除任务的所有标注
     * @param task 任务
     */
    void deleteByTask(Task task);
    
    /**
     * 删除数据项的所有标注
     * @param dataItem 数据项
     */
    void deleteByDataItem(DataItem dataItem);
    
    /**
     * 统计任务标注的不同类别数量
     * @param task 任务
     * @return 类别和数量的配对
     */
    @Query("SELECT a.type, COUNT(a) FROM Annotation a WHERE a.task = :task GROUP BY a.type")
    List<Object[]> countByTaskAndCategory(@Param("task") Task task);
    
    /**
     * 统计任务标注的不同标签数量
     * @param task 任务
     * @return 标签和数量的配对
     */
    @Query("SELECT a.content, COUNT(a) FROM Annotation a WHERE a.task = :task GROUP BY a.content")
    List<Object[]> countByTaskAndLabels(@Param("task") Task task);
    
    /**
     * 统计数据项标注的不同类别数量
     * @param dataItem 数据项
     * @return 类别和数量的配对
     */
    @Query("SELECT a.type, COUNT(a) FROM Annotation a WHERE a.dataItem = :dataItem GROUP BY a.type")
    List<Object[]> countByDataItemAndCategory(@Param("dataItem") DataItem dataItem);
    
    /**
     * 统计用户标注的不同任务数量
     * @param user 用户
     * @return 任务ID和数量的配对
     */
    @Query("SELECT a.task.id, COUNT(a) FROM Annotation a WHERE a.creator = :user GROUP BY a.task.id")
    List<Object[]> countByCreatorAndTask(@Param("user") User user);
    
    /**
     * 统计用户标注的不同类别数量
     * @param user 用户
     * @return 类别和数量的配对
     */
    @Query("SELECT a.type, COUNT(a) FROM Annotation a WHERE a.creator = :user GROUP BY a.type")
    List<Object[]> countByCreatorAndCategory(@Param("user") User user);
    
    /**
     * 统计任务已标注的不同数据项数量
     * @param task 任务
     * @return 已标注的数据项数量
     */
    @Query("SELECT COUNT(DISTINCT a.dataItem) FROM Annotation a WHERE a.task = :task")
    Long countDistinctDataItemByTask(@Param("task") Task task);

    /**
     * 根据任务ID查询标注（分页）
     * 
     * @param taskId 任务ID
     * @param pageable 分页参数
     * @return 标注分页结果
     */
    Page<Annotation> findByTaskId(Long taskId, Pageable pageable);
    
    /**
     * 根据任务ID查询所有标注
     * 
     * @param taskId 任务ID
     * @return 标注列表
     */
    List<Annotation> findByTaskId(Long taskId);
    
    /**
     * 根据数据项ID查询标注
     * 
     * @param dataItemId 数据项ID
     * @return 标注列表
     */
    List<Annotation> findByDataItemId(Long dataItemId);
    
    /**
     * 根据创建者ID查询标注（分页）
     * 
     * @param creatorId 创建者ID
     * @param pageable 分页参数
     * @return 标注分页结果
     */
    Page<Annotation> findByCreatorId(Long creatorId, Pageable pageable);
    
    /**
     * 根据创建者ID和创建时间查询标注
     * 
     * @param creatorId 创建者ID
     * @param startDate 开始时间
     * @return 标注列表
     */
    List<Annotation> findByCreatorIdAndCreatedAtAfter(Long creatorId, LocalDateTime startDate);
    
    /**
     * 根据任务ID和类型查询标注
     * 
     * @param taskId 任务ID
     * @param type 标注类型
     * @return 标注列表
     */
    List<Annotation> findByTaskIdAndType(Long taskId, String type);
    
    /**
     * 根据任务ID和状态查询标注
     * 
     * @param taskId 任务ID
     * @param status 标注状态
     * @return 标注列表
     */
    List<Annotation> findByTaskIdAndStatus(Long taskId, String status);
    
    /**
     * 根据数据项ID和状态查询标注
     * 
     * @param dataItemId 数据项ID
     * @param status 标注状态
     * @return 标注列表
     */
    List<Annotation> findByDataItemIdAndStatus(Long dataItemId, String status);
    
    /**
     * 根据任务ID和创建者ID查询标注
     * 
     * @param taskId 任务ID
     * @param creatorId 创建者ID
     * @return 标注列表
     */
    List<Annotation> findByTaskIdAndCreatorId(Long taskId, Long creatorId);
    
    /**
     * 根据任务ID和审核状态查询标注数量
     * 
     * @param taskId 任务ID
     * @param status 状态（APPROVED或REJECTED）
     * @return 标注数量
     */
    long countByTaskIdAndStatus(Long taskId, String status);
    
    /**
     * 根据创建者ID和审核状态查询标注数量
     * 
     * @param creatorId 创建者ID
     * @param status 状态（APPROVED或REJECTED）
     * @return 标注数量
     */
    long countByCreatorIdAndStatus(Long creatorId, String status);
    
    /**
     * 计算任务已标注数据项的数量
     * 
     * @param taskId 任务ID
     * @return 不同数据项的数量
     */
    @Query("SELECT COUNT(DISTINCT a.dataItem.id) FROM Annotation a WHERE a.task.id = :taskId")
    long countDistinctDataItemByTaskId(@Param("taskId") Long taskId);
    
    /**
     * 按任务和类型统计标注数量
     * 
     * @param taskId 任务ID
     * @return 统计结果
     */
    @Query("SELECT a.type, COUNT(a) FROM Annotation a WHERE a.task.id = :taskId GROUP BY a.type")
    List<Object[]> countByTaskIdGroupByType(@Param("taskId") Long taskId);
    
    /**
     * 按任务和状态统计标注数量
     * 
     * @param taskId 任务ID
     * @return 统计结果
     */
    @Query("SELECT a.status, COUNT(a) FROM Annotation a WHERE a.task.id = :taskId GROUP BY a.status")
    List<Object[]> countByTaskIdGroupByStatus(@Param("taskId") Long taskId);
    
    /**
     * 按创建者和状态统计标注数量
     * 
     * @param creatorId 创建者ID
     * @return 统计结果
     */
    @Query("SELECT a.status, COUNT(a) FROM Annotation a WHERE a.creator.id = :creatorId GROUP BY a.status")
    List<Object[]> countByCreatorIdGroupByStatus(@Param("creatorId") Long creatorId);
} 